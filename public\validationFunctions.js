(function(global) {

    async function validateEmail(email) {
        try {
          const emailResponse = await db.collectionGroup('inviteeSummary')
            .where('sentEmails', 'array-contains', email)
            .get();
      
          if (!emailResponse.empty) {
            const companySnapshot = await db.collection('companies').get();
            
            for (const companyDoc of companySnapshot.docs) {
              const inviteeSummary = await companyDoc.ref.collection('inviteeSummary')
                .doc('summary')
                .get();
              
              if (inviteeSummary.exists && 
                  inviteeSummary.data().sentEmails && 
                  inviteeSummary.data().sentEmails.includes(email)) {
                userCompany = companyDoc.id;
                console.log('Set userCompany to:', userCompany); // Add logging
                
                const validationIcon = document.getElementById("email-validation-icon");
                if (validationIcon) {
                  validationIcon.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>';
                }
                
                const emailInput = document.getElementById("email");
                emailInput.classList.remove('border-red-500');
                emailInput.classList.add('border-green-500');
                isEmailValid = true;
                updateSubmitButton();
                return true;
              }
            }
          }
      
          userCompany = "";
          console.log('Reset userCompany to empty string'); // Add logging
          
          const validationIcon = document.getElementById("email-validation-icon");
          if (validationIcon) {
            validationIcon.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>';
          }
          
          const emailInput = document.getElementById("email");
          emailInput.classList.remove('border-green-500');
          emailInput.classList.add('border-red-500');
          isEmailValid = false;
          updateSubmitButton();
          return false;
        } catch (error) {
          console.error('Error validating email:', error);
          userCompany = ""; // Reset on error
          console.log('Reset userCompany due to error'); // Add logging
          return false;
        }
      }

      // Role validation function
async function validateRole(role) {
    const normalizedRole = role.toLowerCase().trim();
    const roleInput = document.getElementById('role');
    const validationIcon = document.getElementById('role-validation-icon');
  
    // First check if it's in the common roles
    if (commonRolesSet.has(normalizedRole)) {
      showRoleValidationSuccess();
      isRoleValid = true;
      updateSubmitButton();

      // Trigger background generation for common roles too
      triggerBackgroundGenerationForCommonRole(role);

      return true;
    }
  
    // If not in common roles, validate via API
    clearTimeout(debounceTimer);
    debounceTimer = setTimeout(async () => {
      try {
        const response = await fetch('/api/validate-role', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ role })
        });
  
        const result = await response.json();
        
        if (result.isValid) {
          showRoleValidationSuccess();
          isRoleValid = true;
        } else {
          // Show X icon for invalid role
          validationIcon.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>';
          roleInput.classList.remove('border-green-500');
          roleInput.classList.add('border-red-500');
          isRoleValid = false;
          
          // Show notification with debounce
          if (roleDebounceTimer) {
            clearTimeout(roleDebounceTimer);
          }
          roleDebounceTimer = setTimeout(() => {
            showNotification('Please input a valid job role', 'error');
          }, DEBOUNCE_DELAY);
        }
        updateSubmitButton();
      } catch (error) {
        console.error('Error validating role:', error);
        // Show X icon for error state
        validationIcon.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>';
        roleInput.classList.remove('border-green-500');
        roleInput.classList.add('border-red-500');
        isRoleValid = false;
        updateSubmitButton();
  
        if (roleDebounceTimer) {
          clearTimeout(roleDebounceTimer);
        }
        roleDebounceTimer = setTimeout(() => {
          showNotification('An error occurred while validating the role. Please try again.', 'error');
        }, DEBOUNCE_DELAY);
      }
    }, 500);
  }

  function showRoleValidationError(debounced = false) {
    const validationIcon = document.getElementById('role-validation-icon');
    validationIcon.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>';
    roleInput.classList.remove('border-green-500');
    roleInput.classList.add('border-red-500');
    
    if (debounced) {
      if (roleDebounceTimer) {
        clearTimeout(roleDebounceTimer);
      }
      roleDebounceTimer = setTimeout(() => {
        showNotification('Please input a valid job role', 'error');
      }, DEBOUNCE_DELAY);
    }
  }

  function removeErrorNotification() {
    const existingNotifications = document.querySelectorAll('.notification');
    existingNotifications.forEach(notification => {
      if (notification.textContent.includes('Please input a valid job role')) {
        notification.style.opacity = '0';
        setTimeout(() => notification.remove(), 300);
      }
    });
  }

  
function resetRoleValidation() {
    const validationIcon = document.getElementById('role-validation-icon');
    if (validationIcon) {
      validationIcon.innerHTML = '';
    }
    roleInput.classList.remove('border-red-500', 'border-green-500');
    removeErrorNotification();
  }

  function showRoleValidationSuccess() {
    const validationIcon = document.getElementById('role-validation-icon');
    const roleInput = document.getElementById('role');
    validationIcon.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>';
    roleInput.classList.remove('border-red-500');
    roleInput.classList.add('border-green-500');

    // Start background generation monitoring for the validated role
    const role = roleInput.value.trim();
    if (role && typeof startBackgroundGenerationMonitoring === 'function') {
      console.log(`🚀 Starting background generation monitoring for validated role: ${role}`);
      startBackgroundGenerationMonitoring(role);
    }
  }

  // Function to trigger background generation for common roles
  async function triggerBackgroundGenerationForCommonRole(role) {
    try {
      console.log(`🚀 Triggering background generation for common role: ${role}`);

      // Call the server endpoint to trigger background generation
      const response = await fetch('/api/validate-role', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ role })
      });

      if (response.ok) {
        console.log(`✅ Background generation triggered successfully for: ${role}`);
      } else {
        console.warn(`⚠️ Failed to trigger background generation for: ${role}`);
      }
    } catch (error) {
      console.error('Error triggering background generation for common role:', error);
    }
  }

  function resetValidationState() {
    const roleInput = document.getElementById('role');
    const roleValidationIcon = document.getElementById('role-validation-icon');
    
    isEmailValid = false;
    isRoleValid = false;
    
    if (roleValidationIcon) {
      roleValidationIcon.innerHTML = '';
    }
    roleInput.classList.remove('border-red-500', 'border-green-500');
    
    updateSubmitButton();
  }

  function showValidationLoading(inputId) {
    const validationIcon = document.getElementById(`${inputId}-validation-icon`);
    if (validationIcon) {
      validationIcon.innerHTML = '<div class="validation-spinner"></div>';
    }
  }

  // Submission-based validation functions (no immediate UI updates)
  async function validateEmailOnSubmit(email) {
    try {
      // Basic email format validation
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        return {
          isValid: false,
          error: 'Please enter a valid email address',
          errorType: 'format'
        };
      }

      // Check invitation status
      const emailResponse = await db.collectionGroup('inviteeSummary')
        .where('sentEmails', 'array-contains', email)
        .get();

      if (!emailResponse.empty) {
        const companySnapshot = await db.collection('companies').get();

        for (const companyDoc of companySnapshot.docs) {
          const inviteeSummary = await companyDoc.ref.collection('inviteeSummary')
            .doc('summary')
            .get();

          if (inviteeSummary.exists &&
              inviteeSummary.data().sentEmails &&
              inviteeSummary.data().sentEmails.includes(email)) {
            return {
              isValid: true,
              company: companyDoc.id
            };
          }
        }
      }

      return {
        isValid: false,
        error: 'This email address has not been invited to take this assessment',
        errorType: 'invitation'
      };
    } catch (error) {
      console.error('Error validating email:', error);
      return {
        isValid: false,
        error: 'An error occurred while validating your email. Please try again.',
        errorType: 'system'
      };
    }
  }

  async function validateRoleOnSubmit(role) {
    try {
      const normalizedRole = role.toLowerCase().trim();

      // Quick check against common roles
      if (typeof commonRoles !== 'undefined') {
        const commonRolesSet = new Set(commonRoles.map(r => r.toLowerCase()));
        if (commonRolesSet.has(normalizedRole)) {
          // Trigger background generation for common roles
          triggerBackgroundGenerationForCommonRole(role);
          return { isValid: true };
        }
      }

      // API validation for uncommon roles
      const response = await fetch('/api/validate-role', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ role })
      });

      const result = await response.json();

      if (result.isValid) {
        return { isValid: true };
      } else {
        return {
          isValid: false,
          error: 'Please enter a valid job role that represents a genuine occupation'
        };
      }
    } catch (error) {
      console.error('Error validating role:', error);
      return {
        isValid: false,
        error: 'An error occurred while validating the role. Please try again.'
      };
    }
  }

  // Error display functions with shake animations
  function showEmailValidationError(message) {
    const emailInput = document.getElementById('email');
    const errorText = document.getElementById('email-error-text');

    if (emailInput) {
      emailInput.classList.add('input-error', 'shake');
      emailInput.focus();
      setTimeout(() => emailInput.classList.remove('shake'), 600);
    }

    if (errorText) {
      errorText.textContent = message;
      errorText.classList.add('show');
    }
  }

  function showRoleValidationError(message) {
    const roleInput = document.getElementById('role');
    const errorText = document.getElementById('role-error-text');

    if (roleInput) {
      roleInput.classList.add('input-error', 'shake');
      roleInput.focus();
      setTimeout(() => roleInput.classList.remove('shake'), 600);
    }

    if (errorText) {
      errorText.textContent = message;
      errorText.classList.add('show');
    }
  }

  // Error clearing functions
  function clearEmailValidationError() {
    const emailInput = document.getElementById('email');
    const errorText = document.getElementById('email-error-text');

    if (emailInput) {
      emailInput.classList.remove('input-error', 'shake');
    }

    if (errorText) {
      errorText.classList.remove('show');
      errorText.textContent = '';
    }
  }

  function clearRoleValidationError() {
    const roleInput = document.getElementById('role');
    const errorText = document.getElementById('role-error-text');

    if (roleInput) {
      roleInput.classList.remove('input-error', 'shake');
    }

    if (errorText) {
      errorText.classList.remove('show');
      errorText.textContent = '';
    }
  }

  // Validation loading overlay functions
  function showValidationLoadingOverlay() {
    const overlay = document.getElementById('validation-loading-overlay');
    if (overlay) {
      overlay.style.display = 'flex';
    }
  }

  function hideValidationLoadingOverlay() {
    const overlay = document.getElementById('validation-loading-overlay');
    if (overlay) {
      overlay.style.display = 'none';
    }
  }


    global.validateEmail = validateEmail;
    global.validateRole = validateRole;
    global.showRoleValidationError = showRoleValidationError;
    global.removeErrorNotification = removeErrorNotification;
    global.resetRoleValidation = resetRoleValidation;
    global.showRoleValidationSuccess = showRoleValidationSuccess;
    global.resetValidationState = resetValidationState;
    global.showValidationLoading = showValidationLoading;

    // New submission-based validation functions
    global.validateEmailOnSubmit = validateEmailOnSubmit;
    global.validateRoleOnSubmit = validateRoleOnSubmit;
    global.triggerBackgroundGenerationForCommonRole = triggerBackgroundGenerationForCommonRole;
    global.showEmailValidationError = showEmailValidationError;
    global.clearEmailValidationError = clearEmailValidationError;
    global.clearRoleValidationError = clearRoleValidationError;
    global.showValidationLoadingOverlay = showValidationLoadingOverlay;
    global.hideValidationLoadingOverlay = hideValidationLoadingOverlay;
  })(typeof window !== 'undefined' ? window : global);