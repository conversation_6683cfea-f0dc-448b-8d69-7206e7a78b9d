// Test script for progressive loading with early question generation
// This script tests the background generation system

const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

async function testProgressiveLoading() {
  console.log('🧪 Testing Progressive Loading System');
  console.log('=====================================\n');

  try {
    // Test 1: Role Validation and Background Generation Trigger
    console.log('1. Testing role validation and background generation trigger...');
    const testRole = 'Account Manager';
    
    const roleValidationResponse = await axios.post(`${BASE_URL}/api/validate-role`, {
      role: testRole
    });
    
    console.log(`✅ Role validation response:`, roleValidationResponse.data);
    
    if (roleValidationResponse.data.isValid) {
      console.log(`🚀 Background generation should have been triggered for: ${testRole}`);
    }

    // Test 2: Check Background Generation Status
    console.log('\n2. Checking background generation status...');
    
    // Wait a moment for background generation to start
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    const statusResponse = await axios.get(`${BASE_URL}/api/background-generation-status/${encodeURIComponent(testRole)}`);
    console.log(`📊 Background generation status:`, statusResponse.data);

    // Test 3: Monitor Background Generation Progress
    console.log('\n3. Monitoring background generation progress...');
    
    let attempts = 0;
    const maxAttempts = 15; // 30 seconds max
    
    while (attempts < maxAttempts) {
      const progressResponse = await axios.get(`${BASE_URL}/api/background-generation-status/${encodeURIComponent(testRole)}`);
      const status = progressResponse.data;
      
      console.log(`📈 Progress: ${status.completionPercentage}% | Completed: ${status.completed} | Active: ${status.isActive}`);
      
      if (status.completed) {
        console.log('✅ Background generation completed!');
        break;
      }
      
      await new Promise(resolve => setTimeout(resolve, 2000));
      attempts++;
    }

    // Test 4: Test Quiz Generation with Background Questions
    console.log('\n4. Testing quiz generation with background questions...');
    
    const quizResponse = await axios.post(`${BASE_URL}/api/generate-quiz`, {
      role: testRole,
      section: 'foundational',
      framework: { /* minimal framework for testing */ },
      email: '<EMAIL>'
    });
    
    console.log(`📝 Quiz questions received: ${quizResponse.data.length} questions`);
    
    if (quizResponse.data.length === 10) {
      console.log('✅ Correct number of quiz questions received');
    } else {
      console.log('❌ Incorrect number of quiz questions');
    }

    // Test 5: Test Self-Assessment Generation with Background Questions
    console.log('\n5. Testing self-assessment generation with background questions...');
    
    const selfAssessmentResponse = await axios.post(`${BASE_URL}/api/generate-self-assessment`, {
      role: testRole,
      section: 'foundational',
      framework: { /* minimal framework for testing */ },
      email: '<EMAIL>'
    });
    
    console.log(`📋 Self-assessment questions received: ${selfAssessmentResponse.data.length} questions`);
    
    if (selfAssessmentResponse.data.length === 5) {
      console.log('✅ Correct number of self-assessment questions received');
    } else {
      console.log('❌ Incorrect number of self-assessment questions');
    }

    console.log('\n🎉 Progressive Loading Test Completed!');
    console.log('=====================================');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('Response data:', error.response.data);
    }
  }
}

// Performance test
async function testPerformance() {
  console.log('\n⚡ Performance Test');
  console.log('==================\n');

  const testRole = 'Sales Manager';
  
  try {
    // Measure time for role validation + background generation trigger
    const startTime = Date.now();
    
    await axios.post(`${BASE_URL}/api/validate-role`, {
      role: testRole
    });
    
    const validationTime = Date.now() - startTime;
    console.log(`⏱️  Role validation time: ${validationTime}ms`);

    // Wait for background generation to complete
    console.log('⏳ Waiting for background generation...');
    
    let completed = false;
    const bgStartTime = Date.now();
    
    while (!completed && (Date.now() - bgStartTime) < 60000) { // 60 second timeout
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      const statusResponse = await axios.get(`${BASE_URL}/api/background-generation-status/${encodeURIComponent(testRole)}`);
      completed = statusResponse.data.completed;
      
      if (completed) {
        const bgTime = Date.now() - bgStartTime;
        console.log(`✅ Background generation completed in: ${bgTime}ms`);
        break;
      }
    }

    // Measure quiz loading time (should be instant with background generation)
    const quizStartTime = Date.now();
    
    await axios.post(`${BASE_URL}/api/generate-quiz`, {
      role: testRole,
      section: 'foundational',
      framework: {},
      email: '<EMAIL>'
    });
    
    const quizTime = Date.now() - quizStartTime;
    console.log(`🚀 Quiz loading time with background generation: ${quizTime}ms`);
    
    if (quizTime < 1000) {
      console.log('✅ Excellent performance! Quiz loaded in under 1 second');
    } else if (quizTime < 3000) {
      console.log('✅ Good performance! Quiz loaded in under 3 seconds');
    } else {
      console.log('⚠️  Performance could be improved');
    }

  } catch (error) {
    console.error('❌ Performance test failed:', error.message);
  }
}

// Run tests
async function runAllTests() {
  await testProgressiveLoading();
  await testPerformance();
}

// Export for use in other test files
module.exports = {
  testProgressiveLoading,
  testPerformance,
  runAllTests
};

// Run tests if this file is executed directly
if (require.main === module) {
  runAllTests();
}
