// Quick test to verify background generation triggers for common roles
const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

async function testCommonRoleTrigger() {
  console.log('🧪 Testing Common Role Background Generation Trigger');
  console.log('==================================================\n');

  try {
    // Test with a common role that should trigger background generation
    const commonRole = 'Finance Analyst';
    
    console.log(`1. Testing role validation for common role: ${commonRole}`);
    
    const roleValidationResponse = await axios.post(`${BASE_URL}/api/validate-role`, {
      role: commonRole
    });
    
    console.log(`✅ Role validation response:`, roleValidationResponse.data);
    
    if (roleValidationResponse.data.isValid) {
      console.log(`🚀 Background generation should have been triggered for: ${commonRole}`);
      
      // Wait a moment for background generation to start
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // Check background generation status
      const statusResponse = await axios.get(`${BASE_URL}/api/background-generation-status/${encodeURIComponent(commonRole)}`);
      console.log(`📊 Background generation status:`, statusResponse.data);
      
      if (statusResponse.data.isActive || statusResponse.data.completionPercentage > 0) {
        console.log('✅ SUCCESS: Background generation is active for common role!');
      } else {
        console.log('❌ ISSUE: Background generation not detected for common role');
      }
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('Response data:', error.response.data);
    }
  }
}

// Run the test
testCommonRoleTrigger();
